/**
 * UITooltipComponent.ts
 *
 * 工具提示UI组件，用于显示帮助信息和提示文本
 */

import { Vector2 } from 'three';
import { UIComponent, UIComponentProps, UIComponentType } from './UIComponent';

/**
 * 工具提示位置枚举
 */
export enum TooltipPosition {
  TOP = 'top',
  BOTTOM = 'bottom',
  LEFT = 'left',
  RIGHT = 'right',
  TOP_LEFT = 'top-left',
  TOP_RIGHT = 'top-right',
  BOTTOM_LEFT = 'bottom-left',
  BOTTOM_RIGHT = 'bottom-right'
}

/**
 * 工具提示触发方式枚举
 */
export enum TooltipTrigger {
  HOVER = 'hover',
  CLICK = 'click',
  FOCUS = 'focus',
  MANUAL = 'manual'
}

/**
 * 工具提示主题枚举
 */
export enum TooltipTheme {
  DARK = 'dark',
  LIGHT = 'light',
  WARNING = 'warning',
  ERROR = 'error',
  SUCCESS = 'success',
  INFO = 'info'
}

/**
 * 工具提示属性
 */
export interface UITooltipComponentProps extends UIComponentProps {
  // 提示内容
  content?: string;

  // HTML内容
  htmlContent?: string;

  // 目标元素
  target?: HTMLElement | UIComponent;

  // 显示位置
  tooltipPosition?: TooltipPosition;
  
  // 触发方式
  trigger?: TooltipTrigger;
  
  // 主题
  theme?: TooltipTheme;
  
  // 显示延迟（毫秒）
  showDelay?: number;
  
  // 隐藏延迟（毫秒）
  hideDelay?: number;
  
  // 最大宽度
  maxWidth?: number;
  
  // 是否显示箭头
  showArrow?: boolean;
  
  // 箭头大小
  arrowSize?: number;
  
  // 偏移量
  offset?: Vector2;
  
  // 是否禁用
  disabled?: boolean;
  
  // 事件回调
  onShow?: () => void;
  onHide?: () => void;
  onPositionUpdate?: (position: Vector2) => void;
}

/**
 * 工具提示UI组件
 */
export class UITooltipComponent extends UIComponent {
  // 内容属性
  content: string = '';
  htmlContent?: string;
  target?: HTMLElement | UIComponent;
  
  // 显示属性
  position: TooltipPosition = TooltipPosition.TOP;
  trigger: TooltipTrigger = TooltipTrigger.HOVER;
  theme: TooltipTheme = TooltipTheme.DARK;
  showDelay: number = 100;
  hideDelay: number = 100;
  maxWidth: number = 300;
  showArrow: boolean = true;
  arrowSize: number = 6;
  offset: Vector2 = new Vector2(0, 0);
  disabled: boolean = false;
  
  // 事件回调
  onShow?: () => void;
  onHide?: () => void;
  onPositionUpdate?: (position: Vector2) => void;
  
  // 内部状态
  private isVisible: boolean = false;
  private showTimer?: number;
  private hideTimer?: number;
  private targetRect?: DOMRect;
  
  // 内部元素
  private tooltipElement?: HTMLElement;
  private arrowElement?: HTMLElement;
  private contentElement?: HTMLElement;

  /**
   * 构造函数
   */
  constructor(props: UITooltipComponentProps = {}) {
    super({ ...props, type: UIComponentType.TOOLTIP, visible: false });
    
    // 设置属性
    this.content = props.content ?? '';
    this.htmlContent = props.htmlContent;
    this.target = props.target;
    this.position = props.tooltipPosition ?? TooltipPosition.TOP;
    this.trigger = props.trigger ?? TooltipTrigger.HOVER;
    this.theme = props.theme ?? TooltipTheme.DARK;
    this.showDelay = props.showDelay ?? 100;
    this.hideDelay = props.hideDelay ?? 100;
    this.maxWidth = props.maxWidth ?? 300;
    this.showArrow = props.showArrow ?? true;
    this.arrowSize = props.arrowSize ?? 6;
    this.offset = props.offset ?? new Vector2(0, 0);
    this.disabled = props.disabled ?? false;
    this.onShow = props.onShow;
    this.onHide = props.onHide;
    this.onPositionUpdate = props.onPositionUpdate;
    
    // 创建工具提示元素
    this.createTooltipElements();
    
    // 绑定目标元素事件
    if (this.target) {
      this.bindTargetEvents();
    }
  }

  /**
   * 创建工具提示HTML元素
   */
  private createTooltipElements(): void {
    if (!this.htmlElement) return;
    
    // 设置容器样式
    this.htmlElement.className = 'ui-tooltip-container';
    this.htmlElement.style.position = 'absolute';
    this.htmlElement.style.zIndex = '9999';
    this.htmlElement.style.pointerEvents = 'none';
    this.htmlElement.style.visibility = 'hidden';
    this.htmlElement.style.opacity = '0';
    this.htmlElement.style.transition = 'opacity 0.2s ease, visibility 0.2s ease';
    
    // 创建工具提示主体
    this.tooltipElement = document.createElement('div');
    this.tooltipElement.className = 'ui-tooltip';
    this.htmlElement.appendChild(this.tooltipElement);
    
    // 创建内容元素
    this.contentElement = document.createElement('div');
    this.contentElement.className = 'ui-tooltip-content';
    this.tooltipElement.appendChild(this.contentElement);
    
    // 创建箭头元素
    if (this.showArrow) {
      this.arrowElement = document.createElement('div');
      this.arrowElement.className = 'ui-tooltip-arrow';
      this.tooltipElement.appendChild(this.arrowElement);
    }
    
    // 应用样式
    this.applyTooltipStyles();
    
    // 设置内容
    this.updateContent();
  }

  /**
   * 应用工具提示样式
   */
  private applyTooltipStyles(): void {
    if (!this.tooltipElement || !this.contentElement) return;
    
    // 主体样式
    const tooltipStyle = this.tooltipElement.style;
    tooltipStyle.position = 'relative';
    tooltipStyle.maxWidth = `${this.maxWidth}px`;
    tooltipStyle.borderRadius = '4px';
    tooltipStyle.fontSize = '12px';
    tooltipStyle.lineHeight = '1.4';
    tooltipStyle.wordWrap = 'break-word';
    tooltipStyle.filter = 'drop-shadow(0 2px 8px rgba(0, 0, 0, 0.15))';
    
    // 内容样式
    const contentStyle = this.contentElement.style;
    contentStyle.padding = '8px 12px';
    contentStyle.borderRadius = '4px';
    
    // 根据主题设置颜色
    this.applyThemeStyles();
    
    // 箭头样式
    if (this.arrowElement) {
      this.applyArrowStyles();
    }
  }

  /**
   * 应用主题样式
   */
  private applyThemeStyles(): void {
    if (!this.contentElement) return;
    
    const style = this.contentElement.style;
    
    switch (this.theme) {
      case TooltipTheme.DARK:
        style.backgroundColor = '#000000';
        style.color = '#ffffff';
        break;
      case TooltipTheme.LIGHT:
        style.backgroundColor = '#ffffff';
        style.color = '#000000';
        style.border = '1px solid #d9d9d9';
        break;
      case TooltipTheme.WARNING:
        style.backgroundColor = '#faad14';
        style.color = '#ffffff';
        break;
      case TooltipTheme.ERROR:
        style.backgroundColor = '#ff4d4f';
        style.color = '#ffffff';
        break;
      case TooltipTheme.SUCCESS:
        style.backgroundColor = '#52c41a';
        style.color = '#ffffff';
        break;
      case TooltipTheme.INFO:
        style.backgroundColor = '#1890ff';
        style.color = '#ffffff';
        break;
    }
  }

  /**
   * 应用箭头样式
   */
  private applyArrowStyles(): void {
    if (!this.arrowElement || !this.contentElement) return;
    
    const arrowStyle = this.arrowElement.style;
    arrowStyle.position = 'absolute';
    arrowStyle.width = '0';
    arrowStyle.height = '0';
    
    // 获取背景色
    const backgroundColor = this.contentElement.style.backgroundColor;
    
    // 根据位置设置箭头
    this.updateArrowPosition(backgroundColor);
  }

  /**
   * 更新箭头位置
   */
  private updateArrowPosition(backgroundColor: string): void {
    if (!this.arrowElement) return;
    
    const style = this.arrowElement.style;
    const size = this.arrowSize;
    
    // 重置样式
    style.top = 'auto';
    style.bottom = 'auto';
    style.left = 'auto';
    style.right = 'auto';
    style.borderTop = 'none';
    style.borderBottom = 'none';
    style.borderLeft = 'none';
    style.borderRight = 'none';
    
    switch (this.position) {
      case TooltipPosition.TOP:
      case TooltipPosition.TOP_LEFT:
      case TooltipPosition.TOP_RIGHT:
        style.bottom = `-${size}px`;
        style.left = '50%';
        style.transform = 'translateX(-50%)';
        style.borderTop = `${size}px solid ${backgroundColor}`;
        style.borderLeft = `${size}px solid transparent`;
        style.borderRight = `${size}px solid transparent`;
        break;
        
      case TooltipPosition.BOTTOM:
      case TooltipPosition.BOTTOM_LEFT:
      case TooltipPosition.BOTTOM_RIGHT:
        style.top = `-${size}px`;
        style.left = '50%';
        style.transform = 'translateX(-50%)';
        style.borderBottom = `${size}px solid ${backgroundColor}`;
        style.borderLeft = `${size}px solid transparent`;
        style.borderRight = `${size}px solid transparent`;
        break;
        
      case TooltipPosition.LEFT:
        style.right = `-${size}px`;
        style.top = '50%';
        style.transform = 'translateY(-50%)';
        style.borderLeft = `${size}px solid ${backgroundColor}`;
        style.borderTop = `${size}px solid transparent`;
        style.borderBottom = `${size}px solid transparent`;
        break;
        
      case TooltipPosition.RIGHT:
        style.left = `-${size}px`;
        style.top = '50%';
        style.transform = 'translateY(-50%)';
        style.borderRight = `${size}px solid ${backgroundColor}`;
        style.borderTop = `${size}px solid transparent`;
        style.borderBottom = `${size}px solid transparent`;
        break;
    }
  }

  /**
   * 更新内容
   */
  private updateContent(): void {
    if (!this.contentElement) return;
    
    if (this.htmlContent) {
      this.contentElement.innerHTML = this.htmlContent;
    } else {
      this.contentElement.textContent = this.content;
    }
  }

  /**
   * 绑定目标元素事件
   */
  private bindTargetEvents(): void {
    if (!this.target) return;
    
    const targetElement = this.target instanceof UIComponent ? 
      this.target.htmlElement : this.target;
    
    if (!targetElement) return;
    
    switch (this.trigger) {
      case TooltipTrigger.HOVER:
        targetElement.addEventListener('mouseenter', this.handleMouseEnter.bind(this));
        targetElement.addEventListener('mouseleave', this.handleMouseLeave.bind(this));
        break;
        
      case TooltipTrigger.CLICK:
        targetElement.addEventListener('click', this.handleClick.bind(this));
        document.addEventListener('click', this.handleDocumentClick.bind(this));
        break;
        
      case TooltipTrigger.FOCUS:
        targetElement.addEventListener('focus', this.handleFocus.bind(this));
        targetElement.addEventListener('blur', this.handleBlur.bind(this));
        break;
    }
  }

  /**
   * 处理鼠标进入
   */
  private handleMouseEnter(): void {
    if (this.disabled) return;
    this.clearHideTimer();
    this.showTimer = window.setTimeout(() => {
      this.show();
    }, this.showDelay);
  }

  /**
   * 处理鼠标离开
   */
  private handleMouseLeave(): void {
    this.clearShowTimer();
    this.hideTimer = window.setTimeout(() => {
      this.hide();
    }, this.hideDelay);
  }

  /**
   * 处理点击
   */
  private handleClick(): void {
    if (this.disabled) return;
    if (this.isVisible) {
      this.hide();
    } else {
      this.show();
    }
  }

  /**
   * 处理文档点击
   */
  private handleDocumentClick(event: Event): void {
    if (this.trigger !== TooltipTrigger.CLICK) return;
    
    const target = event.target as HTMLElement;
    const targetElement = this.target instanceof UIComponent ? 
      this.target.htmlElement : this.target;
    
    if (targetElement && !targetElement.contains(target) && 
        this.htmlElement && !this.htmlElement.contains(target)) {
      this.hide();
    }
  }

  /**
   * 处理焦点
   */
  private handleFocus(): void {
    if (this.disabled) return;
    this.show();
  }

  /**
   * 处理失焦
   */
  private handleBlur(): void {
    this.hide();
  }

  /**
   * 显示工具提示
   */
  show(): void {
    if (this.disabled || this.isVisible) return;
    
    this.isVisible = true;
    this.updatePosition();
    
    if (this.htmlElement) {
      this.htmlElement.style.visibility = 'visible';
      this.htmlElement.style.opacity = '1';
    }
    
    if (this.onShow) {
      this.onShow();
    }
  }

  /**
   * 隐藏工具提示
   */
  hide(): void {
    if (!this.isVisible) return;
    
    this.isVisible = false;
    
    if (this.htmlElement) {
      this.htmlElement.style.visibility = 'hidden';
      this.htmlElement.style.opacity = '0';
    }
    
    if (this.onHide) {
      this.onHide();
    }
  }

  /**
   * 更新位置
   */
  private updatePosition(): void {
    if (!this.target || !this.htmlElement) return;
    
    const targetElement = this.target instanceof UIComponent ? 
      this.target.htmlElement : this.target;
    
    if (!targetElement) return;
    
    this.targetRect = targetElement.getBoundingClientRect();
    const tooltipRect = this.htmlElement.getBoundingClientRect();
    
    let x = 0;
    let y = 0;
    
    // 根据位置计算坐标
    switch (this.position) {
      case TooltipPosition.TOP:
        x = this.targetRect.left + this.targetRect.width / 2 - tooltipRect.width / 2;
        y = this.targetRect.top - tooltipRect.height - this.arrowSize;
        break;
        
      case TooltipPosition.BOTTOM:
        x = this.targetRect.left + this.targetRect.width / 2 - tooltipRect.width / 2;
        y = this.targetRect.bottom + this.arrowSize;
        break;
        
      case TooltipPosition.LEFT:
        x = this.targetRect.left - tooltipRect.width - this.arrowSize;
        y = this.targetRect.top + this.targetRect.height / 2 - tooltipRect.height / 2;
        break;
        
      case TooltipPosition.RIGHT:
        x = this.targetRect.right + this.arrowSize;
        y = this.targetRect.top + this.targetRect.height / 2 - tooltipRect.height / 2;
        break;
    }
    
    // 应用偏移
    x += this.offset.x;
    y += this.offset.y;
    
    // 边界检查
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };
    
    x = Math.max(0, Math.min(x, viewport.width - tooltipRect.width));
    y = Math.max(0, Math.min(y, viewport.height - tooltipRect.height));
    
    // 设置位置
    this.htmlElement.style.left = `${x}px`;
    this.htmlElement.style.top = `${y}px`;
    
    if (this.onPositionUpdate) {
      this.onPositionUpdate(new Vector2(x, y));
    }
  }

  /**
   * 清除显示定时器
   */
  private clearShowTimer(): void {
    if (this.showTimer) {
      clearTimeout(this.showTimer);
      this.showTimer = undefined;
    }
  }

  /**
   * 清除隐藏定时器
   */
  private clearHideTimer(): void {
    if (this.hideTimer) {
      clearTimeout(this.hideTimer);
      this.hideTimer = undefined;
    }
  }

  /**
   * 设置内容
   */
  setContent(content: string): void {
    this.content = content;
    this.htmlContent = undefined;
    this.updateContent();
  }

  /**
   * 设置HTML内容
   */
  setHTMLContent(htmlContent: string): void {
    this.htmlContent = htmlContent;
    this.updateContent();
  }

  /**
   * 设置目标元素
   */
  setTarget(target: HTMLElement | UIComponent): void {
    this.target = target;
    this.bindTargetEvents();
  }

  /**
   * 销毁组件
   */
  override dispose(): void {
    this.clearShowTimer();
    this.clearHideTimer();
    
    // 移除事件监听器
    if (this.trigger === TooltipTrigger.CLICK) {
      document.removeEventListener('click', this.handleDocumentClick.bind(this));
    }
    
    this.tooltipElement = undefined;
    this.arrowElement = undefined;
    this.contentElement = undefined;
    this.target = undefined;
    this.onShow = undefined;
    this.onHide = undefined;
    this.onPositionUpdate = undefined;
    
    super.dispose();
  }
}
